FROM node:20.9-alpine AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json ./
RUN  npm install 

FROM node:20.9-alpine AS builder

ARG APP_ENV
ENV APP_ENV=$APP_ENV

ENV NEXT_PRIVATE_STANDALONE true

WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

FROM node:20.9-alpine AS runner
WORKDIR /app

ARG APP_ENV
ENV APP_ENV=$APP_ENV

ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder --chown=nextjs:nodejs /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/.secrets ./.secrets
COPY --from=builder /app/package.json ./package.json

RUN chown nextjs:nodejs ./.secrets
RUN chown nextjs:nodejs ./

USER nextjs

EXPOSE 3005

ENV PORT 3005

ENV APP_ENV=$APP_ENV
CMD ["npm", "start"]
