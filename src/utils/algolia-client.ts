import { algoliasearch, Hit } from 'algoliasearch';

export const searchClient = algoliasearch(
  process.env.NEXT_PUBLIC_ALGOLIA_APP_ID || '',
  process.env.NEXT_PUBLIC_ALGOLIA_API_KEY || '',
);

export const transformElementsAndGroup = (hits: Hit<any>[]): Hit<any>[] => {
  const groupedHits = hits.reduce(
    (acc, hit) => {
      const date = new Date(hit.movementDate);
      const hashDate = `${date.getDay()}/${date.getMonth()}/${date.getFullYear()}`;

      const hash = hashDate + hit.wholesalerUserId + hit.branchStore;
      if (!acc[hash]) {
        acc[hash] = {
          wholesalerUserId: hit.wholesalerUserId,
          transactions: [],
          totalAmount: 0,
          quantity: 0,
          hashDate,
          branchStore: hit.branchStore,
        };
      }
      acc[hash].transactions.push({ ...hit, movementDate: new Date(hit.movementDate) });
      acc[hash].totalAmount += hit.totalAmount;
      acc[hash].quantity += 1;
      acc[hash].date = date;
      return acc;
    },
    {} as { [key: string]: Hit<unknown> & { transactions: Hit<unknown>[] } },
  );
  const response = Object.values(groupedHits).map((group: any, index) => ({
    ...group,
    __position: index,
  }));

  return response;
};
