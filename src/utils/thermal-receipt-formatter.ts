import { Transaction } from '@/interfaces/transaction';
import { stringToLongDateFormatted } from './date-operations';
import { numberToCurrencyString } from './string-operations';

export interface ReceiptData {
  transaction: Transaction;
  businessName?: string;
  businessAddress?: string;
  businessPhone?: string;
}

export const formatThermalReceipt = (receiptData: ReceiptData): string => {
  const { transaction, businessName = 'Propaga', businessAddress, businessPhone } = receiptData;

  const RECEIPT_WIDTH = 32;
  const SEPARATOR = '='.repeat(RECEIPT_WIDTH);
  const DASH_LINE = '-'.repeat(RECEIPT_WIDTH);

  const centerText = (text: string): string => {
    if (text.length >= RECEIPT_WIDTH) return text.substring(0, RECEIPT_WIDTH);
    const padding = Math.floor((RECEIPT_WIDTH - text.length) / 2);
    return ' '.repeat(padding) + text;
  };

  const formatTwoColumns = (left: string, right: string): string => {
    const maxLeftLength = RECEIPT_WIDTH - right.length;
    const leftTruncated =
      left.length > maxLeftLength ? left.substring(0, maxLeftLength - 3) + '...' : left;
    const padding = RECEIPT_WIDTH - leftTruncated.length - right.length;
    return leftTruncated + ' '.repeat(Math.max(0, padding)) + right;
  };

  let receipt = '';

  receipt += '\n';
  receipt += centerText(businessName) + '\n';
  if (businessAddress) {
    receipt += centerText(businessAddress) + '\n';
  }
  if (businessPhone) {
    receipt += centerText(businessPhone) + '\n';
  }
  receipt += '\n';
  receipt += centerText('RECIBO DE TRANSACCION') + '\n';
  receipt += SEPARATOR + '\n';
  receipt += '\n';

  receipt += formatTwoColumns('Referencia:', transaction.wholesalerTransactionId) + '\n';
  receipt += formatTwoColumns('Fecha:', stringToLongDateFormatted(transaction.movementDate)) + '\n';
  receipt += '\n';
  receipt += DASH_LINE + '\n';
  receipt += centerText('DETALLE DE COMPRA') + '\n';
  receipt += DASH_LINE + '\n';
  receipt;

  receipt +=
    formatTwoColumns('Monto base:', numberToCurrencyString(transaction.totalAmount)) + '\n';

  if (transaction.interests > 0) {
    receipt += formatTwoColumns('Intereses:', numberToCurrencyString(transaction.interests)) + '\n';
  }

  if (transaction.IVAAmount > 0) {
    receipt += formatTwoColumns('IVA:', numberToCurrencyString(transaction.IVAAmount)) + '\n';
  }

  receipt += DASH_LINE + '\n';
  receipt +=
    formatTwoColumns(
      'TOTAL:',
      numberToCurrencyString(transaction.totalAmountWithInterests || transaction.totalAmount),
    ) + '\n';
  receipt += SEPARATOR + '\n';
  receipt += '\n';

  receipt += centerText('INFORMACION DE PAGO') + '\n';
  receipt += DASH_LINE + '\n';
  receipt += formatTwoColumns('Plazo:', '15 dias') + '\n';
  receipt +=
    formatTwoColumns('Fecha limite:', stringToLongDateFormatted(transaction.paymentDate)) + '\n';
  receipt += '\n';
  receipt += centerText('El cliente pagará después') + '\n';
  receipt += centerText('en el portal de Pagos de Propaga') + '\n';
  receipt += '\n';

  receipt += SEPARATOR + '\n';
  receipt += centerText('Gracias por su compra') + '\n';
  receipt += centerText('Conserve este recibo') + '\n';
  receipt += '\n';
  receipt += '\n';
  receipt += '\n';

  return receipt;
};

export const formatReceiptPreview = (receiptData: ReceiptData): string => {
  const thermalReceipt = formatThermalReceipt(receiptData);
  return thermalReceipt;
};
