import IMAGES from '@/assets/images';
import { AllowedWholesalers, EMPTY_SPACE } from '@/constants';
import { useUser } from '@clerk/nextjs';
const useUserPropaga = () => {
  const appSlang = 'socios';
  const { user } = useUser();

  const getIsUserAllowed = () => {
    if (!user) {
      return true;
    }

    const organizations = user?.organizationMemberships;
    const isAllowed = organizations.some(
      ({ organization }) => organization.slug === `${appSlang}-${process.env.NEXT_PUBLIC_APP_ENV}`,
    );

    return isAllowed;
  };

  const getUserId = () => {
    return user?.id;
  };

  const getWholesalerOrganization = () => {
    if (!user) {
      return null;
    }
    const wholesalerOrganization = user.organizationMemberships.find(({ organization }) =>
      Object.values(AllowedWholesalers).includes(organization.slug as string),
    );

    return wholesalerOrganization;
  };

  const getUserWholesaler = () => {
    const wholesalerOrganization = getWholesalerOrganization();

    if (!wholesalerOrganization) {
      return EMPTY_SPACE;
    }

    return wholesalerOrganization.organization.name;
  };

  const getWholesalerLogo = () => {
    const wholesalerOrganization = getWholesalerOrganization();

    if (!wholesalerOrganization) {
      return IMAGES.IsotipoPropagaLogo;
    }

    return (
      (wholesalerOrganization.organization.publicMetadata.logo as string) ||
      IMAGES.IsotipoPropagaLogo
    );
  };

  const getRole = () => {
    if (!user) {
      return null;
    }

    const org = user.organizationMemberships.find(({ organization }) =>
      Object.values(AllowedWholesalers).includes(organization.slug as string),
    );

    if (!org) {
      return null;
    }

    return org.role;
  };

  const getUserbranchStore = (): string[] | null => {
    if (!user) {
      return null;
    }

    const { publicMetadata: metadata } = user;

    if (!metadata || !metadata.branchStore) {
      return null;
    }

    const branchStore = Array.isArray(metadata.branchStore)
      ? metadata.branchStore
      : [metadata.branchStore];

    return branchStore;
  };

  return {
    appSlang,
    userId: getUserId(),
    isUserAllowed: getIsUserAllowed(),
    userWholesaler: getUserWholesaler(),
    wholesalerLogo: getWholesalerLogo(),
    role: getRole(),
    userbranchStores: getUserbranchStore(),
    username: user?.username || EMPTY_SPACE,
  };
};

export default useUserPropaga;
