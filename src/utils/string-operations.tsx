export const numberToCurrencyString = (number: number) => {
  const currency = Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
  });

  return currency.format(number);
};

export const stringToCurrencyString = (stringNumber: string | null) => {
  if (!stringNumber) {
    return '$0.00';
  }

  const currency = Intl.NumberFormat('es-MX', {
    style: 'currency',
    currency: 'MXN',
  });

  const number = parseFloat(stringNumber);
  return currency.format(number);
};
