'use client';
import { Heading, Text, Box } from '@chakra-ui/react';
import { useClerk, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';

const WAIT_IN_SECONDS_FOR_REDIRECT = 5;
const ONE_SECOND = 1_000;

export default function User() {
  const { isLoaded } = useUser();
  const { signOut } = useClerk();
  const [countdown, setCountdown] = useState(WAIT_IN_SECONDS_FOR_REDIRECT);

  useEffect(() => {
    if (!isLoaded) {
      return;
    }

    const interval = setInterval(() => {
      setCountdown((prevCountdown) => prevCountdown - 1);
    }, ONE_SECOND);

    if (countdown <= 0) {
      clearInterval(interval);
      signOut();
    }

    return () => clearInterval(interval);
  }, [isLoaded, countdown]);

  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      height="100vh"
      textAlign="center"
      p={4}
    >
      <Heading as="h1" size="xl" mb={4}>
        Acceso no autorizado
      </Heading>
      <Text fontSize="xl" mb={4}>
        No tienes permiso para acceder a este sitio.
      </Text>
      <Text fontSize="lg" mb={8}>
        Serás redirigido en {countdown} segundos...
      </Text>
    </Box>
  );
}
