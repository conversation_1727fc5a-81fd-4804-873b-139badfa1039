'use client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CacheProvider } from '@chakra-ui/next-js';
import { ChakraProvider } from '@chakra-ui/react';
import propagaTheme from '@/styles/theme';
import useUserPropaga from '@/utils/use-user-propaga';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useWholesalerContext } from '@/context/wholesaler-context';
import { Roles } from '@/constants';

const queryClient = new QueryClient();

export function Providers({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const { isUserAllowed, userWholesaler, wholesalerLogo, role } = useUserPropaga();
  const { setWholesalerName, setWholesalerLogo } = useWholesalerContext();

  useEffect(() => {
    if (!role || !userWholesaler) {
      return;
    }

    if (!isUserAllowed) {
      router.push('/user-invalid');
      return;
    }
    setWholesalerName(userWholesaler);
    setWholesalerLogo(wholesalerLogo);

    if (role === Roles.MEMBER) {
      router.push('/transaction');
      return;
    }

    router.push('/dashboard');
    return;
  }, [isUserAllowed, userWholesaler, role, wholesalerLogo]);

  return (
    <QueryClientProvider client={queryClient}>
      <CacheProvider>
        <ChakraProvider theme={propagaTheme}>{children}</ChakraProvider>
      </CacheProvider>
    </QueryClientProvider>
  );
}
