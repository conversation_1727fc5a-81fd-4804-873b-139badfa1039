'use client';

import PropagaButton from '@/components/atoms/Button';
import PrintReceiptButton from '@/components/atoms/PrintReceiptButton';
import SuccessIcon from '@/components/icons/SuccessIcon';
import Loading from '@/components/molecules/Loading';
import ReferenceCard from '@/components/molecules/ReferenceCard';
import TransactionSummary from '@/components/organisms/TransactionSummary';
import { useServices } from '@/services';
import COLORS from '@/styles/colors';
import { Flex, Heading, Stack, Text, useToast, VStack } from '@chakra-ui/react';
import { useQuery } from '@tanstack/react-query';
import { useParams, useRouter } from 'next/navigation';
import { useThermalPrinter } from '@/hooks/useThermalPrinter';
import { useEffect, useState } from 'react';

const Page = () => {
  const router = useRouter();
  const { transactionId }: { transactionId: string } = useParams();
  const { transactionService } = useServices();
  const toast = useToast();
  const [autoPrintAttempted, setAutoPrintAttempted] = useState(false);

  const { data: transaction, isLoading } = useQuery({
    queryFn: () => transactionService.getTransaction(transactionId),
    queryKey: ['getTransactionById', transactionId],
  });

  const { isPrinting, printReceipt } = useThermalPrinter({
    businessName: 'Propaga',
    onPrintSuccess: () => {
      console.log('Receipt printed successfully');
    },
    onPrintError: (error) => {
      console.error('Print error:', error);
    },
  });

  // Auto-print receipt when transaction loads
  useEffect(() => {
    if (transaction && !autoPrintAttempted && !isPrinting) {
      setAutoPrintAttempted(true);
      printReceipt(transaction).catch((error) => {
        console.error('Auto-print failed:', error);
      });
    }
  }, [transaction, autoPrintAttempted, isPrinting, printReceipt]);

  if (isLoading) {
    return <Loading />;
  }

  const handleCopyReference = () => {
    navigator.clipboard.writeText(transaction.wholesalerTransactionId);
    toast({
      title: 'Referencia copiada al portapapeles',
      status: 'success',
      position: 'top',
    });
  };

  return (
    <Flex h={'100%'}>
      <Flex w={'50%'} h={'100vh'} py={12} px={40} alignItems="center" justifyContent="center">
        <Stack spacing={12} w={'100%'}>
          <Stack spacing={4} textAlign="center">
            <SuccessIcon />
            <Heading size={'lg'}>Orden completada</Heading>
          </Stack>
          <ReferenceCard
            referenceNumber={transaction.wholesalerTransactionId}
            onClick={handleCopyReference}
          />
          <Stack spacing={4}>
            <Text textColor={COLORS.DarkGray} textAlign={'center'}>
              {isPrinting ? 'Imprimiendo recibo...' : 'Recibo listo para imprimir'}
            </Text>
            <VStack spacing={3} justifyContent="center">
              <PrintReceiptButton
                transaction={transaction}
                variant="outline"
                size="md"
                onPrintStart={() => console.log('Print started')}
                onPrintComplete={() => console.log('Print completed')}
                onPrintError={(error) => console.error('Print error:', error)}
              />
              <PropagaButton
                autoFocus
                onClick={() => {
                  handleCopyReference();
                  router.push('/transaction');
                }}
              >
                Continuar
              </PropagaButton>
            </VStack>
          </Stack>
        </Stack>
      </Flex>
      <Flex w={'50%'} h={'100vh'} py={20} px={12}>
        <TransactionSummary transaction={transaction} />
      </Flex>
    </Flex>
  );
};

export default Page;
