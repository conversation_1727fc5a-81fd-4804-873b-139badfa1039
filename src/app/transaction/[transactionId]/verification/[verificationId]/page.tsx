'use client';
import PropagaButton from '@/components/atoms/Button';
import OtpInput from '@/components/atoms/OtpInput';
import Loading from '@/components/molecules/Loading';
import ErrorDisplay from '@/components/organisms/ErrorDisplay';
import { EMPTY_SPACE, ErrorCodes, WAIT_IN_SECONDS_FOR_RETRY } from '@/constants';
import { ErrorResponse } from '@/interfaces/error-response';
import { useServices } from '@/services';
import { stringToCurrencyString } from '@/utils/string-operations';
import { Center, Heading, Stack, Text, Box, useToast } from '@chakra-ui/react';
import { useMutation } from '@tanstack/react-query';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

export default function Page() {
  const router = useRouter();
  const { transactionId, verificationId }: { transactionId: string; verificationId: string } =
    useParams();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(false);
  const [isCodeError, setIsCodeError] = useState(false);
  const [countdown, setCountdown] = useState(WAIT_IN_SECONDS_FOR_RETRY);
  const [isError, setIsError] = useState(false);

  const inputRef = useRef(null);
  const { transactionService } = useServices();
  const toast = useToast();
  const totalAmount = searchParams.get('totalAmount');

  useEffect(() => {
    if (inputRef.current) {
      (inputRef.current as HTMLInputElement).focus();
    }

    const interval = setInterval(() => {
      setCountdown((prevCountdown) => prevCountdown - 1);
    }, 1000);

    if (countdown === 0) {
      clearInterval(interval);
    }

    return () => clearInterval(interval);
  }, []);

  const validateVerification = useMutation({
    mutationFn: transactionService.validateVerificationCode,
    onSuccess: () => {
      router.push(`/transaction/${transactionId}/success`);
    },
    onError: (error: ErrorResponse) => {
      const { response } = error;
      setIsLoading(false);

      if (response.data.errorCode === ErrorCodes.VERIFICATION_NOT_VALID.code) {
        setIsCodeError(true);
        return;
      }
    },
  });

  const resendVerification = useMutation({
    mutationFn: transactionService.resendVerificationCode,
    onSuccess: () => {
      setIsLoading(false);
      toast({
        title: 'Código reenviado',
        description: 'Se ha reenviado el código de verificación',
        status: 'success',
        position: 'top',
      });
    },
    onError: () => {
      setIsLoading(false);
      setIsError(true);
    },
  });

  const cancelTransaction = useMutation({
    mutationFn: transactionService.cancelTransaction,
    onSuccess: () => {
      setIsLoading(false);
      toast({
        title: 'Orden cancelada',
        description: 'La orden ha sido cancelada con éxito',
        status: 'success',
        position: 'top',
      });

      router.push(`/transaction`);
    },
    onError: () => {
      setIsLoading(false);
      setIsError(true);
    },
  });

  const handleOnComplete = async (value: string) => {
    setIsLoading(true);

    await validateVerification.mutateAsync({
      transactionId,
      verificationId,
      code: value,
    });
  };

  const handleResendCode = () => {
    setIsLoading(true);
    resendVerification.mutateAsync({
      transactionId,
      verificationId,
    });
  };

  const handleTransactionCancel = async () => {
    setIsLoading(true);
    cancelTransaction.mutateAsync(transactionId);
  };

  if (isLoading) {
    return <Loading />;
  }

  if (isError) {
    return (
      <ErrorDisplay
        message={'Ha ocurrido un error al intentar confirmar la orden'}
        referenceNumber={EMPTY_SPACE}
        onClick={() => {
          router.push('/transaction');
        }}
      />
    );
  }

  return (
    <Center>
      <Stack spacing={8} w={'30%'} py={12} textAlign={'center'}>
        <Heading
          size={'lg'}
        >{`Ingresa el código para confirmar la orden por ${stringToCurrencyString(totalAmount)}`}</Heading>
        <Text>Pídele el código al cliente. Lo puede ver en la app y en WhatsApp.</Text>
        <Box>
          <OtpInput onComplete={handleOnComplete} ref={inputRef} />
          {isCodeError && (
            <Text fontSize="md" mb="40px" className="poppins" color={'red.500'} p={2} mt={2}>
              El código ingresado no es válido
            </Text>
          )}
        </Box>

        <Box>
          <Text size={'sm'} pb={4}>
            {countdown > 0
              ? `Tienes ${countdown} segundos para confirmar`
              : 'Confirma la orden o reenvia el código'}
          </Text>
          <PropagaButton>Continuar</PropagaButton>
          <PropagaButton theme={'tertiary'} onClick={handleResendCode}>
            Enviar de nuevo el código
          </PropagaButton>
          <PropagaButton theme={'secondary'} onClick={handleTransactionCancel}>
            Cancelar orden
          </PropagaButton>
        </Box>
      </Stack>
    </Center>
  );
}
