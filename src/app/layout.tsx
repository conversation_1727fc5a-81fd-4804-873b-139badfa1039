'use client';
import { Clerk<PERSON>rov<PERSON> } from '@clerk/nextjs';
import { Providers } from './providers';
import favicon from '../../public/favicon.ico';
import { WholesalerProvider } from '@/context/wholesaler-context';
import cookiesPersistor from '@/context/persistor';
import { esMX } from '@clerk/localizations';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <head>
        <title>Propaga Socios</title>
        <link rel="icon" href={favicon.src} />
      </head>
      <body>
        <ClerkProvider
          localization={esMX}
          appearance={{
            elements: {
              footerAction: { display: 'none' },
            },
          }}
          afterSignOutUrl={'/'}
        >
          <WholesalerProvider persistAdapter={cookiesPersistor}>
            <Providers>{children}</Providers>
          </WholesalerProvider>
        </ClerkProvider>
      </body>
    </html>
  );
}
