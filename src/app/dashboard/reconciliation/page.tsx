'use client';
import React, { useState } from 'react';
import {
  Box,
  Flex,
  Heading,
  Select,
  Stack,
  Text,
  FormControl,
  FormLabel,
  Button,
  useClipboard,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Badge,
  Input,
  InputGroup,
  InputLeftElement,
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  IconButton,
} from '@chakra-ui/react';
import {
  SearchIcon,
  DownloadIcon,
  DeleteIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from '@chakra-ui/icons';
import DatePicker from 'react-datepicker';
import '@/styles/date-picker.css';
import 'react-datepicker/dist/react-datepicker.css';
import { useEffect } from 'react';
import { useServices } from '@/services';
import { useQuery } from '@tanstack/react-query';

export default function ReconciliationPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [status, setStatus] = useState<'pending-transfer' | 'transfered' | undefined>();
  const { onCopy } = useClipboard('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [page, setPage] = useState(1);
  const { wholesalerService } = useServices();

  const { data } = useQuery({
    queryKey: ['wholesalerTransfers', page, searchTerm, status, startDate, endDate],
    queryFn: () =>
      wholesalerService.getWholesalerTransfers({
        page,
        referenceNumber: searchTerm || undefined,
        startDate: startDate?.toISOString(),
        endDate: endDate?.toISOString(),
        status,
      }),
  });

  const reconciliationsData = data?.data || [];
  const totalPages = data?.pagination.totalPages;

  const onDateChange = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;
    setStartDate(start);
    setEndDate(end);
    setPage(1);
  };

  useEffect(() => {
    setPage(1);
  }, [searchTerm, status]);

  const getStatusColor = (status: 'pending-transfer' | 'transfered') => {
    const statusColors = {
      'pending-transfer': 'yellow',
      transfered: 'green',
    };
    return statusColors[status] || 'gray';
  };

  const handleDownloadCSV = (allTransactions: any[]) => {
    const headers = [
      'ID de Transacción',
      'Monto',
      'ID de Usuario',
      'ID de Cajero',
      'Sucursal',
      'Fecha',
    ];
    const csvContent = [
      headers.join(','),
      ...allTransactions.map((t) =>
        [
          t.id,
          `$${t.totalAmount.toFixed(2)}`,
          t.userExternalId,
          t.wholesalerUserId,
          t.branchStore,
          new Date(t.movementDate).toLocaleString('es-MX'),
        ].join(','),
      ),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `transactions-${new Date().toISOString()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Box p={8}>
      <Stack spacing={6}>
        <Flex justify="left" align="end">
          <Heading size="lg">Liquidación</Heading>
        </Flex>

        <Stack direction="row" spacing={4} align="end" justify={'space-between'}>
          <FormControl maxW="300px">
            <InputGroup>
              <InputLeftElement pointerEvents="none">
                <SearchIcon color="gray.300" />
              </InputLeftElement>
              <Input
                placeholder="Buscar por folio..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </InputGroup>
          </FormControl>
          <FormControl maxW="200px">
            <Select
              value={status}
              onChange={(e) => setStatus(e.target.value as 'pending-transfer' | 'transfered')}
            >
              <option value="">Todos</option>
              <option value="pending-transfer">Pendiente</option>
              <option value="transfered">Completado</option>
            </Select>
          </FormControl>
          <FormControl maxW="300px">
            <Flex gap={2} justify={'center'} align={'center'}>
              <Box flex={1}>
                <DatePicker
                  selected={startDate}
                  onChange={onDateChange}
                  startDate={startDate}
                  endDate={endDate}
                  selectsRange
                  dateFormat="dd/MM"
                  placeholderText="Seleccionar fechas"
                  className="chakra-select css-161pkch"
                />
              </Box>
              <IconButton
                aria-label="Reset date filter"
                icon={<DeleteIcon />}
                variant="ghost"
                onClick={() => {
                  setStartDate(null);
                  setEndDate(null);
                }}
              />
            </Flex>
          </FormControl>
        </Stack>

        {reconciliationsData.map((reconciliationData: any) => (
          <Box key={reconciliationData.id} borderWidth="1px" borderRadius="lg" p={6}>
            <Stack spacing={4}>
              <Flex align={'center'} justify={'space-between'}>
                <Flex justify="left" align="center">
                  <Heading size="md">
                    {reconciliationData.status === 'pending-transfer'
                      ? reconciliationData.generatedReferenceNumber
                      : reconciliationData.referenceNumber}
                  </Heading>
                  <Badge
                    colorScheme={getStatusColor(reconciliationData.status)}
                    ml={4}
                    borderRadius="md"
                  >
                    {reconciliationData.status === 'pending-transfer' ? 'Pendiente' : 'Completado'}
                  </Badge>
                </Flex>

                <Button
                  leftIcon={<DownloadIcon />}
                  onClick={() => handleDownloadCSV(reconciliationData.transactions)}
                  colorScheme="blue"
                  variant="outline"
                  size={'sm'}
                >
                  Descargar CSV
                </Button>
              </Flex>

              <Stack spacing={3}>
                <Flex justify="space-between">
                  <Text>Inicio del corte</Text>
                  <Text>{reconciliationData.startDate}</Text>
                </Flex>
                <Flex justify="space-between">
                  <Text>Fin del corte</Text>
                  <Text>{reconciliationData.cutoffDate}</Text>
                </Flex>
              </Stack>

              <Box bg="gray.50" p={4} borderRadius="md">
                <Stack spacing={3}>
                  <Flex justify="space-between">
                    <Text>Monto total de transacciones:</Text>
                    <Text fontWeight="bold">
                      $
                      {reconciliationData.transactionsAmount.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                    </Text>
                  </Flex>
                  <Flex justify="space-between">
                    <Text>Monto total a transferir:</Text>
                    <Text fontWeight="bold">
                      $
                      {reconciliationData.totalAmount.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2,
                      })}
                    </Text>
                  </Flex>
                </Stack>
              </Box>

              <Stack spacing={4}>
                <FormControl>
                  <FormLabel>Número de referencia</FormLabel>
                  <Flex gap={2}>
                    <Text flex={1} p={2} bg="gray.100" borderRadius="md">
                      {reconciliationData.status === 'pending-transfer'
                        ? reconciliationData.generatedReferenceNumber
                        : reconciliationData.referenceNumber}
                    </Text>
                    <Button onClick={onCopy}>Copiar</Button>
                  </Flex>
                </FormControl>
              </Stack>

              <Accordion allowMultiple>
                <AccordionItem>
                  <AccordionButton>
                    <Box flex="1" textAlign="left">
                      Transacciones a pagar ({reconciliationData.transactions.length})
                    </Box>
                    <AccordionIcon />
                  </AccordionButton>
                  <AccordionPanel pb={4}>
                    <Table variant="simple" size="sm">
                      <Thead>
                        <Tr>
                          <Th>Folio</Th>
                          <Th>Fecha</Th>
                          <Th>Sucursal</Th>
                          <Th>Monto</Th>
                          <Th>ID</Th>
                          <Th>ID de Cajero</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {reconciliationData.transactions.map((transaction: any) => (
                          <Tr key={transaction.id}>
                            <Td>{transaction.wholesalerTransactionId}</Td>
                            <Td>{new Date(transaction.movementDate).toLocaleString('es-MX')}</Td>
                            <Td>{transaction.branchStore}</Td>
                            <Td>
                              $
                              {transaction.totalAmount.toLocaleString('en-US', {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              })}
                            </Td>
                            <Td>{transaction.userExternalId}</Td>
                            <Td>{transaction.wholesalerUserId}</Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </AccordionPanel>
                </AccordionItem>
              </Accordion>
            </Stack>
          </Box>
        ))}

        <Flex justify="center" mt={6} gap={2}>
          <IconButton
            aria-label="Página anterior"
            icon={<ChevronLeftIcon />}
            onClick={() => setPage(page - 1)}
            isDisabled={page === 1}
          />
          <Text alignSelf="center">
            Página {page} de {totalPages}
          </Text>
          <IconButton
            aria-label="Siguiente página"
            icon={<ChevronRightIcon />}
            onClick={() => setPage(page + 1)}
            isDisabled={page === totalPages}
          />
        </Flex>
      </Stack>
    </Box>
  );
}
