'use client';
import { SignIn, useAuth } from '@clerk/nextjs';
import { Spinner, Center, Box, Flex } from '@chakra-ui/react';
import { useRouter } from 'next/navigation';

export default function Home() {
  const router = useRouter();
  const { isLoaded, userId } = useAuth();

  if (!isLoaded) {
    return (
      <Center>
        <Spinner />
      </Center>
    );
  }

  if (userId) {
    router.push('/transaction');
  }

  return (
    <Flex minH={'100vh'} align={'center'} justify={'center'}>
      <Box rounded={'lg'} p={8}>
        <SignIn forceRedirectUrl={'/transaction'} routing="hash" />
      </Box>
    </Flex>
  );
}
