export type Product = {
  id: string;
  externalSKU: string;
  name: string;
  quantity: number;
};

export type Transaction = {
  id: string;
  totalAmount: number;
  interests: number;
  IVAAmount: number;
  totalAmountWithInterests: number;
  cornerStoreId: string;
  movementDate: string;
  products: Product[];
  wholesalerTransactionId: string;
  wholesalerUserId: string;
  paymentDate: string;
  status: 'completed' | 'cancelled';
};
