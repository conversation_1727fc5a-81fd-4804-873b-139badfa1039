import { useState, useCallback } from 'react';
import { useToast } from '@chakra-ui/react';
import { Transaction } from '@/interfaces/transaction';
import { thermalPrinter, PrintResult, PrinterConfig } from '@/services/thermal-printer';
import { ReceiptData } from '@/utils/thermal-receipt-formatter';

export interface UseThermalPrinterOptions {
  businessName?: string;
  businessAddress?: string;
  businessPhone?: string;
  printerConfig?: PrinterConfig;
  onPrintSuccess?: (result: PrintResult) => void;
  onPrintError?: (error: Error) => void;
}

export interface UseThermalPrinterReturn {
  isPrinting: boolean;
  printReceipt: (transaction: Transaction) => Promise<void>;
  testPrinter: () => Promise<void>;
  lastPrintResult: PrintResult | null;
}

/**
 * Custom hook for thermal printer functionality
 * Provides easy-to-use interface for printing receipts with error handling and loading states
 */
export const useThermalPrinter = (
  options: UseThermalPrinterOptions = {},
): UseThermalPrinterReturn => {
  const [isPrinting, setIsPrinting] = useState(false);
  const [lastPrintResult, setLastPrintResult] = useState<PrintResult | null>(null);
  const toast = useToast();

  const {
    businessName = 'Propaga',
    businessAddress,
    businessPhone,
    onPrintSuccess,
    onPrintError,
  } = options;

  /**
   * Print a transaction receipt
   */
  const printReceipt = useCallback(
    async (transaction: Transaction): Promise<void> => {
      if (isPrinting) {
        toast({
          title: 'Impresión en progreso',
          description: 'Por favor espere a que termine la impresión actual',
          status: 'warning',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      setIsPrinting(true);

      try {
        const businessInfo: Partial<ReceiptData> = {
          businessName,
          businessAddress,
          businessPhone,
        };

        const result = await thermalPrinter.printReceipt(transaction, businessInfo);
        setLastPrintResult(result);

        if (result.success) {
          toast({
            title: 'Recibo impreso',
            description: result.message,
            status: 'success',
            duration: 3000,
            isClosable: true,
          });

          onPrintSuccess?.(result);
        } else {
          throw new Error(result.message);
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Error desconocido al imprimir';

        const errorResult: PrintResult = {
          success: false,
          message: errorMessage,
          error: error instanceof Error ? error : new Error(errorMessage),
        };

        setLastPrintResult(errorResult);

        toast({
          title: 'Error al imprimir',
          description: errorMessage,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });

        onPrintError?.(error instanceof Error ? error : new Error(errorMessage));
      } finally {
        setIsPrinting(false);
      }
    },
    [isPrinting, businessName, businessAddress, businessPhone, toast, onPrintSuccess, onPrintError],
  );

  /**
   * Test printer connectivity
   */
  const testPrinter = useCallback(async (): Promise<void> => {
    if (isPrinting) {
      toast({
        title: 'Impresión en progreso',
        description: 'Por favor espere a que termine la impresión actual',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsPrinting(true);

    try {
      const result = await thermalPrinter.testPrinter();
      setLastPrintResult(result);

      if (result.success) {
        toast({
          title: 'Prueba de impresora exitosa',
          description: result.message,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error al probar la impresora';

      toast({
        title: 'Error en prueba de impresora',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsPrinting(false);
    }
  }, [isPrinting, toast]);

  return {
    isPrinting,
    printReceipt,
    testPrinter,
    lastPrintResult,
  };
};
