import { extendTheme } from '@chakra-ui/react';
import COLORS from './colors';
import { Poppins, Quicksand } from 'next/font/google';

const quicksand = Quicksand({
  subsets: ['latin'],
  display: 'swap',
  weight: ['300', '400', '500', '600', '700'],
});

const poppins = Poppins({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
});

const propagaTheme = extendTheme({
  fonts: {
    heading: quicksand.style.fontFamily,
    body: poppins.style.fontFamily,
  },
  styles: {
    global: () => ({
      body: {
        color: 'default',
        bg: '#FFFF',
      },
    }),
  },
  components: {
    Card: {
      container: {
        backgroundColor: '#e7e7e7',
      },
      header: {
        paddingBottom: '2px',
      },
      body: {
        paddingTop: '2px',
      },
      footer: {
        paddingTop: '2px',
      },
      variants: {
        primary: {
          container: {
            backgroundColor: '#FFF',
            borderRadius: '5px',
          },
        },
      },
    },
    Button: {
      variants: {
        primary: {
          color: 'white',
          size: 'sm',
          rounded: 'md',
          backgroundColor: COLORS.Primary,
          _hover: { bg: COLORS.Hover },
          fontSize: '14px',
          fontWidth: '100',
        },
        linkButton: {
          backgroundColor: COLORS.Primary,
        },
      },
    },
    Heading: {
      variants: {
        success: {
          color: COLORS.Primary,
        },
        error: {
          color: COLORS.Error,
        },
      },
    },
    Input: {
      variants: {
        propaga: {
          field: {
            bg: 'transparent',
            border: '1px',
            borderColor: COLORS.Gray,
            _focus: {
              borderColor: COLORS.Primary,
            },
          },
        },
        error: {
          field: {
            bg: 'transparent',
            border: '1px',
            borderColor: COLORS.Error,
            _focus: {
              borderColor: COLORS.Error,
            },
          },
        },
      },
    },
  },
});

export default propagaTheme;
