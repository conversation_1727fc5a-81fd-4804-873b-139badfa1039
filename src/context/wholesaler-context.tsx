import IMAGES from '@/assets/images';
import { EMPTY_SPACE } from '@/constants';
import { PersistContextProvider } from '@/interfaces/persist-context-adapter';
import React, { createContext, useContext, useState } from 'react';

type WholesalerContextType = {
  wholesalerName: string;
  wholesalerLogo: string;
  setWholesalerName: (wholesaler: string) => void;
  setWholesalerLogo: (wholesalerLogoUrl: string) => void;
  removeWholesaler: () => void;
};

const WholesalerContext = createContext<WholesalerContextType | undefined>(undefined);

export const useWholesalerContext = () => {
  const context = useContext(WholesalerContext);
  if (!context) {
    throw new Error('useWholesalerContext must be used within a WholesalerContextProvider');
  }
  return context;
};

export const WholesalerProvider = ({
  children,
  persistAdapter,
}: {
  children: React.ReactNode;
  persistAdapter: PersistContextProvider;
}) => {
  const [wholesalerName, setWholesalerNameState] = useState<string>(
    () => persistAdapter.retrieve('wholesaler') || EMPTY_SPACE,
  );
  const [wholesalerLogo, setWholesalerLogoState] = useState<string>(
    () => persistAdapter.retrieve('wholesalerLogo') || IMAGES.IsotipoPropagaLogo,
  );

  const setWholesalerName = (wholesaler: string) => {
    setWholesalerNameState(wholesaler);
    persistAdapter.persist('wholesaler', wholesaler);
  };

  const removeWholesaler = () => {
    setWholesalerNameState('');
    setWholesalerLogoState(IMAGES.IsotipoPropagaLogo);
    persistAdapter.remove('wholesaler');
    persistAdapter.remove('wholesalerLogo');
  };

  const setWholesalerLogo = (wholesalerLogoUrl: string) => {
    setWholesalerLogoState(wholesalerLogoUrl);
    persistAdapter.persist('wholesalerLogo', wholesalerLogoUrl);
  };

  return (
    <WholesalerContext.Provider
      value={{
        wholesalerName,
        wholesalerLogo,
        setWholesalerName,
        setWholesalerLogo,
        removeWholesaler,
      }}
    >
      {children}
    </WholesalerContext.Provider>
  );
};
