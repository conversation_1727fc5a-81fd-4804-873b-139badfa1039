import { PersistContextProvider } from '@/interfaces/persist-context-adapter';
import Cookies from 'js-cookie';

const cookiesPersistor: PersistContextProvider = {
  persist: (key: string, value: any): void => {
    if (typeof value === 'string') {
      Cookies.set(key, value, { expires: 365 });
      return;
    }

    Cookies.set(key, JSON.stringify(value), { expires: 365 });
  },
  retrieve: (key: string) => {
    const data = Cookies.get(key);

    if (!data) {
      return undefined;
    }

    try {
      return JSON.parse(data);
    } catch (e) {
      return data;
    }
  },
  remove: (key: string): void => {
    Cookies.remove(key);
  },
};

export default cookiesPersistor;
