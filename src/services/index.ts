import Axios from 'axios';
import { CornerStoreServiceFactory } from './corner-store';
import { TransactionServiceFactory } from './transaction';
import { WholesalerServiceFactory } from './wholesaler';
import { useWholesalerContext } from '@/context/wholesaler-context';
import { WholesalerNames } from '@/constants';

const getWholesalerToken = (wholesalerName: string) => {
  switch (wholesalerName) {
    case WholesalerNames.SCORPION:
      return process.env.NEXT_PUBLIC_SCORPION_API_TOKEN;
    case WholesalerNames.GRUPO_MODELO:
      return process.env.NEXT_PUBLIC_GRUPO_MODELO_API_TOKEN;
    case WholesalerNames.GASCOM:
      return process.env.NEXT_PUBLIC_GASCOM_API_TOKEN;
    case WholesalerNames.IMPULSORA:
      return process.env.NEXT_PUBLIC_IMPULSORA_API_TOKEN;
    case WholesalerNames.SURTACE:
      return process.env.NEXT_PUBLIC_SURTACE_API_TOKEN;
    default:
      return process.env.NEXT_PUBLIC_PROPAGA_API_TOKEN;
  }
};

const createHttpInstance = (wholesalerName: string) => {
  return Axios.create({
    baseURL: process.env.NEXT_PUBLIC_BASE_API_URL,
    headers: {
      Authorization: getWholesalerToken(wholesalerName),
      'X-Wholesaler': wholesalerName,
    },
  });
};

export const useServices = () => {
  const { wholesalerName } = useWholesalerContext();
  const httpInstance = createHttpInstance(wholesalerName);

  const cornerStoreService = CornerStoreServiceFactory(httpInstance);
  const transactionService = TransactionServiceFactory(httpInstance);
  const wholesalerService = WholesalerServiceFactory(httpInstance);

  return {
    cornerStoreService,
    transactionService,
    wholesalerService,
  };
};
