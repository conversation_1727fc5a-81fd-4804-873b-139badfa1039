import { AxiosInstance } from 'axios';
export const TransactionServiceFactory = (httpClient: AxiosInstance) => {
  const createTransaction = async ({
    wholesalerTransactionId,
    cornerStoreId,
    totalAmount,
    products,
    wholesalerUserId,
    metadata,
    branchStore,
  }: {
    wholesalerTransactionId: string;
    cornerStoreId: string;
    totalAmount: number;
    wholesalerUserId: string;
    products: { externalSKU: string; name: string; quantity: number }[];
    metadata?: object;
    branchStore?: string;
  }) => {
    const response = await httpClient.post('/v1/transaction', {
      wholesalerTransactionId,
      cornerStoreId,
      totalAmount,
      products,
      wholesalerUserId,
      metadata,
      branchStore,
    });

    return response.data;
  };

  const validateVerificationCode = async ({
    transactionId,
    verificationId,
    code,
  }: {
    transactionId: string;
    verificationId: string;
    code: string;
  }) => {
    const response = await httpClient.put(
      `/v1/transaction/${transactionId}/verification/${verificationId}`,
      {
        code,
      },
    );

    return response.data;
  };

  const resendVerificationCode = async ({
    transactionId,
    verificationId,
  }: {
    transactionId: string;
    verificationId: string;
  }) => {
    const response = await httpClient.post(
      `/v1/transaction/${transactionId}/verification/${verificationId}`,
    );

    return response.data;
  };

  const getTransaction = async (transactionId: string) => {
    const response = await httpClient.get(`/v1/transaction/${transactionId}`);

    return response.data;
  };

  const createSimulation = async (cornerStoreId: string, totalAmount: string) => {
    const response = await httpClient.post('/v1/transaction/simulation', {
      cornerStoreId,
      totalAmount,
    });

    return response.data;
  };

  const createVerification = async (transactionId: string) => {
    const response = await httpClient.post(`/v1/transaction/${transactionId}/verification`);
    return response.data;
  };

  const cancelTransaction = async (transactionId: string) => {
    const response = await httpClient.put(`/v1/transaction/${transactionId}`, {
      status: 'cancel',
      cancellationReason: 'CANCELLED_BY_WHOLESALER',
    });

    return response.data;
  };

  const getTransactionsByWholesalerUserId = async (wholesalerUserId: string | undefined) => {
    if (!wholesalerUserId) {
      return;
    }

    const response = await httpClient.get(`/v1/transaction?wholesalerUserId=${wholesalerUserId}`);
    return response.data;
  };

  return {
    createTransaction,
    validateVerificationCode,
    resendVerificationCode,
    getTransaction,
    createSimulation,
    createVerification,
    cancelTransaction,
    getTransactionsByWholesalerUserId,
  };
};
