import { AxiosInstance } from 'axios';
export const WholesalerServiceFactory = (httpClient: AxiosInstance) => {
  const getCustomizerOptions = async () => {
    const response = await httpClient.get('/v1/wholesaler/customizer-options');
    return response.data;
  };

  const getWholesalerTransfers = async (params: {
    page?: number;
    referenceNumber?: string;
    startDate?: string;
    endDate?: string;
    status?: string;
  }) => {
    const response = await httpClient.get('/v1/wholesaler/transfers', {
      params,
    });
    return response.data;
  };

  return {
    getCustomizerOptions,
    getWholesalerTransfers,
  };
};
