export const DEFAULT_WHOLESALER = 'Propaga';
export const EMPTY_SPACE = '';
export const ENTER = 'Enter';
export const TAB = 'Tab';
export const ErrorCodes = Object.freeze({
  VERIFICATION_NOT_VALID: {
    code: 'VERIFICATION_NOT_VALID',
    message: 'El código de verificación no es válido',
  },
  CREDIT_LIMIT_EXCEEDED: {
    code: 'CREDIT_LIMIT_EXCEEDED',
    message: 'El cliente no tiene suficiente saldo, cancelamos la orden',
  },
  MINIMAL_TRANSACTION_REQUIRED: {
    code: 'MINIMAL_TRANSACTION_REQUIRED',
    message: 'El monto del pedido debe ser mayor a $700',
  },
  USER_IS_IN_DEFAULT: {
    code: 'USER_IS_IN_DEFAULT',
    message: 'El cliente tiene una deuda pendiente, cancelamos la orden',
  },
});

export const DEFAULT_ERROR_MESSAGE = 'Ha ocurrido un error al intentar generar la orden';

export const WAIT_IN_SECONDS_FOR_RETRY = 30;

export const WholesalerNames = Object.freeze({
  SCORPION: 'Scorpion',
  RINTIN: 'Rintin',
  PROPAGA: 'Propaga',
  GRUPO_MODELO: 'Grupo Modelo',
  GASCOM: 'Gascom',
  MERCANTO: 'Mercanto',
  IMPULSORA: 'Impulsora',
  ABA: 'Aba',
  SURTACE: 'Surtace',
});

export const Roles = Object.freeze({
  ADMIN: 'org:admin',
  MEMBER: 'org:member',
});

export const AllowedWholesalers = Object.freeze([
  'scorpion',
  'grupo-modelo',
  'gascom',
  'propaga',
  'impulsora',
  'aba',
  'surtace',
]);
