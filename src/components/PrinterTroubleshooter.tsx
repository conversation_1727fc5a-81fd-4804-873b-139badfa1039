import React, { useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Text,
  VStack,
  HStack,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Badge,
  Code,
  List,
  ListItem,
  ListIcon,
  useToast,
} from '@chakra-ui/react';
import { CheckCircleIcon } from '@chakra-ui/icons';

interface TroubleshootingStep {
  title: string;
  description: string;
  action?: () => Promise<void>;
  actionLabel?: string;
  severity: 'info' | 'warning' | 'error';
}

const PrinterTroubleshooter: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<
    Array<{ step: string; success: boolean; message: string }>
  >([]);
  const toast = useToast();

  const troubleshootingSteps: TroubleshootingStep[] = [
    {
      title: 'Check Browser Compatibility',
      description: 'Verify that your browser supports Web USB API',
      action: async () => {
        const supported = 'usb' in navigator;
        setResults((prev) => [
          ...prev,
          {
            step: 'Browser Check',
            success: supported,
            message: supported
              ? 'Web USB API is supported'
              : 'Web USB API not supported - use Chrome, Edge, or Opera',
          },
        ]);
      },
      actionLabel: 'Check Browser',
      severity: 'error',
    },
    {
      title: 'Close Other Applications',
      description: 'Ensure no other applications are using the printer',
      action: async () => {
        // This is a manual step, so we just provide guidance
        setResults((prev) => [
          ...prev,
          {
            step: 'Close Applications',
            success: true,
            message:
              'Please manually close any applications that might be using the printer (POS software, printer utilities, etc.)',
          },
        ]);
      },
      actionLabel: 'Check Applications',
      severity: 'warning',
    },
    {
      title: 'Reset USB Connection',
      description: 'Disconnect and reconnect the USB cable',
      action: async () => {
        setResults((prev) => [
          ...prev,
          {
            step: 'USB Reset',
            success: true,
            message: 'Please unplug the USB cable, wait 5 seconds, then reconnect it',
          },
        ]);
      },
      actionLabel: 'Reset USB',
      severity: 'warning',
    },
    {
      title: 'Check Device Permissions',
      description: 'Verify that the browser has permission to access USB devices',
      action: async () => {
        try {
          const devices = await (navigator as any).usb.getDevices();
          setResults((prev) => [
            ...prev,
            {
              step: 'Device Permissions',
              success: true,
              message: `Found ${devices.length} previously authorized device(s)`,
            },
          ]);
        } catch (error: any) {
          setResults((prev) => [
            ...prev,
            {
              step: 'Device Permissions',
              success: false,
              message: `Permission check failed: ${error.message}`,
            },
          ]);
        }
      },
      actionLabel: 'Check Permissions',
      severity: 'error',
    },
    {
      title: 'Test Device Detection',
      description: 'Try to detect the printer device',
      action: async () => {
        try {
          const device = await (navigator as any).usb.requestDevice({
            filters: [{ vendorId: 0x04b8 }], // Epson
          });
          setResults((prev) => [
            ...prev,
            {
              step: 'Device Detection',
              success: true,
              message: `Device detected: ${device.productName || 'Unknown Epson Printer'}`,
            },
          ]);
        } catch (error: any) {
          setResults((prev) => [
            ...prev,
            {
              step: 'Device Detection',
              success: false,
              message: `Device detection failed: ${error.message}`,
            },
          ]);
        }
      },
      actionLabel: 'Detect Device',
      severity: 'error',
    },
  ];

  const runFullDiagnostic = async () => {
    setIsRunning(true);
    setResults([]);

    for (const step of troubleshootingSteps) {
      if (step.action) {
        await step.action();
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    setIsRunning(false);

    const successCount = results.filter((r) => r.success).length;
    toast({
      title: 'Diagnostic Complete',
      description: `${successCount}/${results.length} checks passed`,
      status: successCount === results.length ? 'success' : 'warning',
      duration: 5000,
    });
  };

  const clearBrowserData = async () => {
    try {
      // Clear USB device permissions
      const devices = await (navigator as any).usb.getDevices();
      for (const device of devices) {
        try {
          await device.forget();
        } catch (error) {
          // Ignore errors when forgetting devices
        }
      }

      toast({
        title: 'Browser Data Cleared',
        description: 'USB device permissions have been reset. Please refresh the page.',
        status: 'success',
        duration: 5000,
      });
    } catch (error: any) {
      toast({
        title: 'Clear Failed',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'red';
      case 'warning':
        return 'orange';
      default:
        return 'blue';
    }
  };

  return (
    <Card>
      <CardHeader>
        <Heading size="md">🔧 Printer Troubleshooter</Heading>
        <Text color="gray.600" mt={2}>
          Diagnose and fix common "Access denied" and connection issues
        </Text>
      </CardHeader>

      <CardBody>
        <VStack spacing={6} align="stretch">
          {/* Quick Actions */}
          <Box>
            <Heading size="sm" mb={3}>
              Quick Actions
            </Heading>
            <HStack spacing={3} wrap="wrap">
              <Button
                colorScheme="blue"
                onClick={runFullDiagnostic}
                isLoading={isRunning}
                loadingText="Running Diagnostic..."
              >
                Run Full Diagnostic
              </Button>
              <Button colorScheme="orange" variant="outline" onClick={clearBrowserData}>
                Clear Browser Data
              </Button>
              <Button colorScheme="gray" variant="outline" onClick={() => window.location.reload()}>
                Refresh Page
              </Button>
            </HStack>
          </Box>

          {/* Diagnostic Results */}
          {results.length > 0 && (
            <Box>
              <Heading size="sm" mb={3}>
                Diagnostic Results
              </Heading>
              <VStack spacing={2} align="stretch">
                {results.map((result, index) => (
                  <Alert key={index} status={result.success ? 'success' : 'error'}>
                    <AlertIcon />
                    <Box>
                      <AlertTitle>{result.step}</AlertTitle>
                      <AlertDescription>{result.message}</AlertDescription>
                    </Box>
                  </Alert>
                ))}
              </VStack>
            </Box>
          )}

          {/* Manual Troubleshooting Steps */}
          <Box>
            <Heading size="sm" mb={3}>
              Manual Troubleshooting Steps
            </Heading>
            <Accordion allowMultiple>
              {troubleshootingSteps.map((step, index) => (
                <AccordionItem key={index}>
                  <AccordionButton>
                    <Box flex="1" textAlign="left">
                      <HStack>
                        <Badge colorScheme={getSeverityColor(step.severity)}>
                          {step.severity.toUpperCase()}
                        </Badge>
                        <Text fontWeight="semibold">{step.title}</Text>
                      </HStack>
                    </Box>
                    <AccordionIcon />
                  </AccordionButton>
                  <AccordionPanel pb={4}>
                    <VStack spacing={3} align="start">
                      <Text>{step.description}</Text>
                      {step.action && (
                        <Button
                          size="sm"
                          colorScheme={getSeverityColor(step.severity)}
                          onClick={step.action}
                          isDisabled={isRunning}
                        >
                          {step.actionLabel}
                        </Button>
                      )}
                    </VStack>
                  </AccordionPanel>
                </AccordionItem>
              ))}
            </Accordion>
          </Box>

          {/* Common Error Solutions */}
          <Box>
            <Heading size="sm" mb={3}>
              Common Error Solutions
            </Heading>
            <VStack spacing={4} align="stretch">
              <Alert status="error">
                <AlertIcon />
                <Box>
                  <AlertTitle>"Access denied" Error</AlertTitle>
                  <AlertDescription>
                    <List spacing={1} mt={2}>
                      <ListItem>
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        Close all other applications using the printer
                      </ListItem>
                      <ListItem>
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        Disconnect and reconnect the USB cable
                      </ListItem>
                      <ListItem>
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        Restart the printer
                      </ListItem>
                      <ListItem>
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        Clear browser USB permissions and try again
                      </ListItem>
                    </List>
                  </AlertDescription>
                </Box>
              </Alert>

              <Alert status="warning">
                <AlertIcon />
                <Box>
                  <AlertTitle>"Device not found" Error</AlertTitle>
                  <AlertDescription>
                    <List spacing={1} mt={2}>
                      <ListItem>
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        Ensure printer is powered on
                      </ListItem>
                      <ListItem>
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        Check USB cable connection
                      </ListItem>
                      <ListItem>
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        Try a different USB port
                      </ListItem>
                      <ListItem>
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        Install printer drivers if needed
                      </ListItem>
                    </List>
                  </AlertDescription>
                </Box>
              </Alert>

              <Alert status="info">
                <AlertIcon />
                <Box>
                  <AlertTitle>Browser Requirements</AlertTitle>
                  <AlertDescription>
                    <Text mt={2}>Web USB API requires:</Text>
                    <List spacing={1} mt={2}>
                      <ListItem>
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        Chrome 61+, Edge 79+, or Opera 48+
                      </ListItem>
                      <ListItem>
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        HTTPS connection or localhost
                      </ListItem>
                      <ListItem>
                        <ListIcon as={CheckCircleIcon} color="green.500" />
                        User permission for each device
                      </ListItem>
                    </List>
                  </AlertDescription>
                </Box>
              </Alert>
            </VStack>
          </Box>

          {/* Advanced Solutions */}
          <Box>
            <Heading size="sm" mb={3}>
              Advanced Solutions
            </Heading>
            <Code display="block" whiteSpace="pre-wrap" p={3} fontSize="sm">
              {`If the issue persists, try these advanced steps:

1. Check Windows Device Manager:
   - Look for "Universal Serial Bus devices"
   - Find your Epson printer
   - Update driver if there's a warning icon

2. Reset Chrome USB permissions:
   - Go to chrome://settings/content/usbDevices
   - Remove all Epson devices
   - Try connecting again

3. Check printer mode:
   - Some printers have multiple modes (ESC/POS vs Windows driver)
   - Ensure printer is in ESC/POS mode

4. Test with different browser:
   - Try Microsoft Edge if using Chrome
   - Ensure browser is up to date`}
            </Code>
          </Box>
        </VStack>
      </CardBody>
    </Card>
  );
};

export default PrinterTroubleshooter;
