import { Icon } from '@chakra-ui/react';

const SuccessIcon = () => {
  return (
    <Icon width="86" height="86" viewBox="0 0 86 86" color="red.500">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        fill="#3ABD79"
        d="M43 86C19.2425 86 0 66.7575 0 43C0 19.2425 19.2425 0 43 0C66.7575 0 86 19.2425 86 43C86 66.7575 66.7575 86 43 86ZM62.4575 28.9175C61.1677 27.6277 59.1252 27.6277 57.8354 28.9175L37.3029 49.9875L28.165 40.5273C26.8752 39.2375 24.8327 39.2375 23.5429 40.5273C22.2531 41.8171 22.2531 43.9677 23.5429 45.2575L35.0458 57.0829C36.3355 58.3727 38.3781 58.3727 39.6679 57.0829L62.5654 33.6479C63.7479 32.3574 63.7479 30.2067 62.4574 28.9169L62.4575 28.9175Z"
      />
    </Icon>
  );
};

export default SuccessIcon;
