import COLORS from '@/styles/colors';
import { Button } from '@chakra-ui/react';
import { forwardRef, LegacyRef } from 'react';

type Props = {
  children: React.ReactNode;
  isDisabled?: boolean;
  theme?: 'primary' | 'secondary' | 'tertiary';
  onClick?: () => void;
  isLoading?: boolean;
  autoFocus?: boolean;
};

const PropagaButton = forwardRef(
  (
    { children, isDisabled = false, onClick, isLoading, theme, autoFocus }: Props,
    ref: LegacyRef<HTMLButtonElement>,
  ) => {
    const themeEnum: {
      [key: string]: { backgroundColor: string; textColor: string; hoverColor: string };
    } = {
      primary: {
        backgroundColor: COLORS.BlackAlpha,
        textColor: 'white',
        hoverColor: COLORS.DarkGray,
      },
      secondary: {
        backgroundColor: COLORS.Error,
        textColor: 'white',
        hoverColor: COLORS.ErrorLigth,
      },
      tertiary: {
        backgroundColor: COLORS.WhiteAlpha,
        textColor: 'black',
        hoverColor: COLORS.Gray,
      },
    };

    const buttonTheme = theme ? themeEnum[theme] : themeEnum.primary;

    return (
      <Button
        autoFocus={autoFocus}
        size={'lg'}
        height={'60px'}
        width={'full'}
        rounded={'full'}
        backgroundColor={buttonTheme.backgroundColor}
        color={buttonTheme.textColor}
        mt={4}
        isDisabled={isDisabled}
        onClick={onClick}
        isLoading={isLoading}
        ref={ref}
        _hover={{
          backgroundColor: buttonTheme.hoverColor,
        }}
      >
        {children}
      </Button>
    );
  },
);

export default PropagaButton;
