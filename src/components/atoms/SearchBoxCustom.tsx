import { useSearchBox } from 'react-instantsearch';
import {
  Box,
  Input,
  InputGroup,
  InputLeftElement,
  InputRightElement,
  IconButton,
} from '@chakra-ui/react';
import { SearchIcon, CloseIcon } from '@chakra-ui/icons';

const SearchBoxCustom = () => {
  const { query, refine, clear } = useSearchBox();

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    refine(event.target.value);
  };

  return (
    <Box position="relative">
      <InputGroup>
        <InputLeftElement pointerEvents="none">
          <SearchIcon color="gray.400" />
        </InputLeftElement>
        <Input
          type="text"
          placeholder="Search..."
          value={query}
          onChange={handleChange}
          pr="2.5rem"
        />
        {query && (
          <InputRightElement>
            <IconButton
              aria-label="Clear search"
              icon={<CloseIcon />}
              size="sm"
              variant="ghost"
              onClick={clear}
            />
          </InputRightElement>
        )}
      </InputGroup>
    </Box>
  );
};

export default SearchBoxCustom;
