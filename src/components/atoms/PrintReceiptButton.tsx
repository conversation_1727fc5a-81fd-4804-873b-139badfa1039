import React from 'react';
import { Button, ButtonProps, Icon } from '@chakra-ui/react';
import { Transaction } from '@/interfaces/transaction';
import { useThermalPrinter, UseThermalPrinterOptions } from '@/hooks/useThermalPrinter';

// Print icon component (you can replace this with an actual icon from your icon library)
const PrintIcon = (props: any) => (
  <Icon viewBox="0 0 24 24" {...props}>
    <path
      fill="currentColor"
      d="M18 3H6v4h12V3zm1 5H5c-1.1 0-2 .9-2 2v6h4v4h10v-4h4v-6c0-1.1-.9-2-2-2zm-1 5H6v-2h12v2z"
    />
  </Icon>
);

export interface PrintReceiptButtonProps extends Omit<ButtonProps, 'onClick'> {
  transaction: Transaction;
  printerOptions?: UseThermalPrinterOptions;
  variant?: 'primary' | 'secondary' | 'outline';
  showIcon?: boolean;
  onPrintStart?: () => void;
  onPrintComplete?: () => void;
  onPrintError?: (error: Error) => void;
}

/**
 * Print Receipt Button Component
 * A reusable button component for printing transaction receipts
 */
const PrintReceiptButton: React.FC<PrintReceiptButtonProps> = ({
  transaction,
  printerOptions = {},
  variant = 'primary',
  showIcon = true,
  onPrintStart,
  onPrintComplete,
  onPrintError,
  children,
  isDisabled,
  ...buttonProps
}) => {
  const { isPrinting, printReceipt } = useThermalPrinter({
    ...printerOptions,
    onPrintSuccess: (result) => {
      onPrintComplete?.();
      printerOptions.onPrintSuccess?.(result);
    },
    onPrintError: (error) => {
      onPrintError?.(error);
      printerOptions.onPrintError?.(error);
    },
  });

  const handlePrint = async () => {
    onPrintStart?.();
    await printReceipt(transaction);
  };

  const getVariantProps = () => {
    switch (variant) {
      case 'primary':
        return {
          colorScheme: 'blue',
          variant: 'solid',
        };
      case 'secondary':
        return {
          colorScheme: 'gray',
          variant: 'solid',
        };
      case 'outline':
        return {
          colorScheme: 'blue',
          variant: 'outline',
        };
      default:
        return {
          colorScheme: 'blue',
          variant: 'solid',
        };
    }
  };

  return (
    <Button
      {...getVariantProps()}
      {...buttonProps}
      onClick={handlePrint}
      isLoading={isPrinting}
      loadingText="Imprimiendo..."
      isDisabled={isDisabled || isPrinting}
      leftIcon={showIcon ? <PrintIcon /> : undefined}
    >
      {children || 'Imprimir Recibo'}
    </Button>
  );
};

export default PrintReceiptButton;
