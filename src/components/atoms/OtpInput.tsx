import { HStack, PinInput, PinInputField } from '@chakra-ui/react';
import { forwardRef, LegacyRef } from 'react';

const OtpInput = forwardRef(
  ({ onComplete }: { onComplete: (value: string) => void }, ref: LegacyRef<HTMLInputElement>) => {
    return (
      <HStack>
        <PinInput type={'number'} otp placeholder="0" onComplete={onComplete}>
          <PinInputField w={'40%'} h={'80px'} fontSize={'4xl'} ref={ref} />
          <PinInputField w={'40%'} h={'80px'} fontSize={'4xl'} />
          <PinInputField w={'40%'} h={'80px'} fontSize={'4xl'} />
          <PinInputField w={'40%'} h={'80px'} fontSize={'4xl'} />
        </PinInput>
      </HStack>
    );
  },
);

export default OtpInput;
