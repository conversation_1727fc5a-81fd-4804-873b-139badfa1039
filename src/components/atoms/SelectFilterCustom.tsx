import { useRefinementList } from 'react-instantsearch';
import { Box, Select } from '@chakra-ui/react';

interface RefinementListCustomProps {
  attribute: string;
  placeholder?: string;
}

const SelectFilterCustom = ({
  attribute,
  placeholder = 'Select an option',
}: RefinementListCustomProps) => {
  const { items, refine } = useRefinementList({
    attribute,
  });

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = event.target.value;
    if (selectedValue) {
      const item = items.find((item) => item.value === selectedValue);
      if (item) {
        refine(item.value);
      }
    }
  };

  return (
    <Box>
      <Select placeholder={placeholder} onChange={handleChange} size="md" variant="outline">
        {items.map((item) => (
          <option key={item.value} value={item.value}>
            {item.label} ({item.count})
          </option>
        ))}
      </Select>
    </Box>
  );
};

export default SelectFilterCustom;
