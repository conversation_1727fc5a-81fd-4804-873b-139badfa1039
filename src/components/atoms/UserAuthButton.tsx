import { UserButton } from '@clerk/nextjs';

const UserAuthButton = () => {
  return (
    <UserButton
      userProfileProps={{
        appearance: {
          elements: {
            profileSection__emailAddresses: {
              display: 'none',
            },
            profileSection__username: {
              display: 'none',
            },
          },
        },
      }}
      appearance={{
        elements: {
          userButtonPopoverFooter: {
            display: 'none',
          },
        },
      }}
    />
  );
};

export default UserAuthButton;
