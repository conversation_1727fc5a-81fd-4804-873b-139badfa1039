import COLORS from '@/styles/colors';
import { Tag, TagLabel, TagLeftIcon } from '@chakra-ui/react';
import PointSuccessIcon from './PointSuccessIcon';
import PointErrorIcon from './PointErrorIcon';

const StatusBadge = ({ status }: { status: 'completed' | 'cancelled' }) => {
  const statusEmun: {
    [key: string]: { backgroundColor: string; color: string; text: string };
  } = {
    completed: {
      backgroundColor: COLORS.SuccessLight,
      color: COLORS.Success,
      text: 'Completada',
    },
    cancelled: {
      backgroundColor: COLORS.ErrorLigth,
      color: COLORS.Error,
      text: 'Cancelada',
    },
  };

  return (
    <Tag
      variant={'subtle'}
      backgroundColor={statusEmun[status].backgroundColor}
      size={'lg'}
      borderRadius={'full'}
      p={2}
      px={4}
    >
      <TagLeftIcon as={status === 'completed' ? PointSuccessIcon : PointErrorIcon} />
      <TagLabel ml={2} color={statusEmun[status].color}>
        {statusEmun[status].text}
      </TagLabel>
    </Tag>
  );
};

export default StatusBadge;
