import { RangeInput } from 'react-instantsearch';
import { Box, Flex, IconButton } from '@chakra-ui/react';
import { DeleteIcon } from '@chakra-ui/icons';
import DatePicker from 'react-datepicker';
import { useState } from 'react';
import '@/styles/date-picker.css';

interface DateRangeFilterCustomProps {
  attribute: string;
  placeholder?: string;
}
const DateRangeFilterCustom = ({
  attribute,
  placeholder = 'Selecionar fechas',
}: DateRangeFilterCustomProps) => {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  const [startDateUnix, setStartDateUnix] = useState<number>(0);
  const [endDateUnix, setEndDateUnix] = useState<number>(Number.MAX_VALUE);

  const handleDateChange = (dates: [Date | null, Date | null]) => {
    const [start, end] = dates;

    setStartDate(start);
    setEndDate(end);

    if (start && end) {
      setStartDateUnix(start.getTime());
      setEndDateUnix(end.setHours(23, 59, 59));
    }
  };

  const handleReset = () => {
    setStartDateUnix(0);
    setEndDateUnix(Number.MAX_VALUE);
    setStartDate(null);
    setEndDate(null);
  };

  return (
    <Box className="cursor" w={'100%'}>
      <Flex gap={2} justify={'center'} align={'center'}>
        <Box flex={1} w={'90%'}>
          <DatePicker
            selected={startDate}
            onChange={handleDateChange}
            startDate={startDate}
            endDate={endDate}
            selectsRange
            dateFormat="dd/MM/yyyy"
            placeholderText={placeholder}
            className="chakra-select css-161pkch"
            isClearable={false}
            maxDate={new Date()}
          />
          <RangeInput
            style={{ display: 'none' }}
            attribute={attribute}
            min={startDateUnix}
            max={endDateUnix}
          />
        </Box>
        <IconButton
          aria-label="Reset date filter"
          icon={<DeleteIcon />}
          variant="ghost"
          onClick={handleReset}
        />
      </Flex>
    </Box>
  );
};

export default DateRangeFilterCustom;
