import COLORS from '@/styles/colors';
import { Input, InputGroup, InputLeftElement, InputRightElement, Spinner } from '@chakra-ui/react';
import { forwardRef, HTMLInputTypeAttribute } from 'react';
import { CheckIcon } from '@chakra-ui/icons';
import { NumericFormat } from 'react-number-format';

type PropagaInputProps = {
  type?: HTMLInputTypeAttribute | undefined;
  isCurrencyInput?: boolean;
  autoFocus?: boolean;
  isLoading?: boolean;
  isSuccess?: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
};

const PropagaInput = forwardRef(
  (
    {
      type = 'text',
      isCurrencyInput = false,
      autoFocus = false,
      isLoading = false,
      isSuccess = false,
      onChange,
      onKeyDown,
    }: PropagaInputProps,
    ref: React.Ref<HTMLInputElement>,
  ) => {
    const handleFocus = (event: any) => event.target.select();

    if (isCurrencyInput) {
      return (
        <InputGroup>
          <InputLeftElement pointerEvents={'none'} fontSize={'1.2em'} pt={2} fontWeight={'bold'}>
            $
          </InputLeftElement>
          <Input
            as={NumericFormat}
            size={'lg'}
            maxLength={8}
            autoFocus={autoFocus}
            focusBorderColor={COLORS.Primary}
            getInputRef={ref}
            onChange={onChange}
            onFocus={handleFocus}
            onKeyDown={onKeyDown}
            pl={8}
            thousandSeparator={','}
            decimalSeparator={'.'}
          />
        </InputGroup>
      );
    }

    return (
      <InputGroup>
        <Input
          type={type}
          size={'lg'}
          focusBorderColor={COLORS.Primary}
          ref={ref}
          onFocus={handleFocus}
          onChange={onChange}
          onKeyDown={onKeyDown}
        />
        <InputRightElement py={2}>
          {isLoading && <Spinner size={'sm'} />}
          {isSuccess && <CheckIcon color="green.500" mt={2} />}
        </InputRightElement>
      </InputGroup>
    );
  },
);

export default PropagaInput;
