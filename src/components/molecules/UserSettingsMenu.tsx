import { SettingsIcon } from '@chakra-ui/icons';
import { IconButton, Menu, MenuButton, MenuItem, MenuList } from '@chakra-ui/react';

const UserSettingsMenu = ({ onSignOut }: { onSignOut: () => void }) => {
  return (
    <Menu>
      <MenuButton as={IconButton} aria-label="Settings" icon={<SettingsIcon />} variant="ghost" />
      <MenuList>
        <MenuItem onClick={onSignOut}>Cerrar sesión</MenuItem>
      </MenuList>
    </Menu>
  );
};

export default UserSettingsMenu;
