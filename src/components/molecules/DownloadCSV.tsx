import { DownloadIcon } from '@chakra-ui/icons';
import { Button } from '@chakra-ui/react';

const DownloadCSV = ({ filteredData }: { filteredData: any }) => {
  const handleDownloadCSV = (filteredData: any) => {
    const allTransactions = filteredData.flatMap((row: any) =>
      row.transactions.map((t: any) => ({
        ...t,
        date: row.date,
        sucursal: row.sucursal,
      })),
    );

    const headers = [
      'ID de Transacción',
      'ID Externo',
      'Producto',
      'Monto',
      'Método de Pago',
      'Cantidad',
      'Total',
      'Sucursal',
      'Fecha',
    ];

    const csvContent = [
      headers.join(','),
      ...allTransactions.map((t: any) => [t.id].join(',')),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `transactions-${new Date().toISOString()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Button
      leftIcon={<DownloadIcon />}
      onClick={() => handleDownloadCSV(filteredData)}
      colorScheme="blue"
      variant="outline"
      size="md"
      width={{ base: '100%', md: 'auto' }}
      mt={{ base: 0, md: 8 }}
    >
      Descargar CSV
    </Button>
  );
};

export default DownloadCSV;
