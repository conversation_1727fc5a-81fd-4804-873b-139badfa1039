import { Card, CardBody, Flex, HStack, Link, Stack, Text } from '@chakra-ui/react';

import { CopyIcon } from '@chakra-ui/icons';

const ReferenceCard = ({
  referenceNumber,
  onClick,
}: {
  referenceNumber: string;
  onClick: () => void;
}) => {
  return (
    <Card variant={'outline'} borderRadius={12}>
      <CardBody>
        <HStack spacing={2} w={'100%'}>
          <Stack w={'92%'}>
            <Text size={'sm'}>Referencia</Text>
            <Text fontWeight={'bold'} fontSize={'lg'}>
              {referenceNumber}
            </Text>
          </Stack>
          <Flex w={'8%'}>
            <Link onClick={onClick}>
              <CopyIcon boxSize={8} />
            </Link>
          </Flex>
        </HStack>
      </CardBody>
    </Card>
  );
};

export default ReferenceCard;
