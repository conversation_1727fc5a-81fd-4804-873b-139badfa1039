import { Card, CardBody, Divider, Flex, HStack, Stack, Text } from '@chakra-ui/react';
import StatusBadge from '../atoms/StatusBadge';
import { numberToCurrencyString } from '@/utils/string-operations';
import { stringToLongDateFormatted } from '@/utils/date-operations';

const TransactionCard = ({
  wholesalerTransactionId,
  createdBy,
  totalAmount,
  movementDate,
  status,
}: {
  wholesalerTransactionId: string;
  createdBy: string;
  totalAmount: number;
  movementDate: string;
  status: 'completed' | 'cancelled';
}) => {
  return (
    <Card variant={'outline'} borderRadius={12}>
      <CardBody>
        <HStack spacing={2} w={'100%'}>
          <Stack w={'70%'}>
            <Text fontWeight={'bold'} fontSize={'xl'}>
              {wholesalerTransactionId}
            </Text>
            <Text size={'sm'}>{createdBy}</Text>
          </Stack>
          <Flex w={'30%'}>
            <Text fontWeight={'bold'} fontSize={'lg'} w={'100%'} textAlign={'right'}>
              {numberToCurrencyString(totalAmount)}
            </Text>
          </Flex>
        </HStack>
        <Divider my={4} w={'100%'} />
        <HStack spacing={2} w={'100%'}>
          <Flex w={'50%'}>
            <StatusBadge status={status} />
          </Flex>
          <Flex w={'50%'}>
            <Text size={'sm'} w={'100%'} textAlign={'right'}>
              {stringToLongDateFormatted(movementDate)}
            </Text>
          </Flex>
        </HStack>
      </CardBody>
    </Card>
  );
};

export default TransactionCard;
