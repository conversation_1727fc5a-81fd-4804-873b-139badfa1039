import { Box, Flex, Heading, Stack } from '@chakra-ui/react';
import PropagaButton from '../atoms/Button';
import ErrorIcon from '../icons/ErrorIcon';
import ReferenceCard from '../molecules/ReferenceCard';
import { forwardRef } from 'react';

type Props = {
  message: string;
  referenceNumber?: string;
  onClick: () => void;
};

const ErrorDisplay = forwardRef(({ message, referenceNumber, onClick }: Props) => {
  return (
    <Flex w="100%" h="100vh" alignItems="center" justifyContent="center">
      <Stack spacing={4} w="30%">
        <ErrorIcon />
        <Heading size="lg" textAlign="center" mt={4}>
          {message}
        </Heading>
        <Box pt={8}>
          {referenceNumber && <ReferenceCard referenceNumber={referenceNumber} onClick={onClick} />}
        </Box>
        <PropagaButton autoFocus onClick={onClick}>
          Regresar
        </PropagaButton>
      </Stack>
    </Flex>
  );
});

export default ErrorDisplay;
