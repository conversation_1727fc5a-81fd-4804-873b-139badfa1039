import { Transaction } from '@/interfaces/transaction';
import TransactionCard from '../molecules/TransactionCard';
import { Stack, Text } from '@chakra-ui/react';
import COLORS from '@/styles/colors';
import Loading from '../molecules/Loading';

const TransactionCardList = ({
  transactions,
  createdBy,
}: {
  transactions: Transaction[];
  createdBy: string;
}) => {
  if (!transactions) {
    return <Loading />;
  }

  if (!transactions.length) {
    return (
      <Text color={COLORS.DarkGray} textAlign={'center'} p={4} fontSize={'sm'}>
        Sin transacciones realizadas el día de hoy
      </Text>
    );
  }

  return (
    <Stack spacing={6} w={'100%'} overflowY={'auto'}>
      {transactions.map((transaction, index) => (
        <TransactionCard
          key={index}
          wholesalerTransactionId={transaction.wholesalerTransactionId}
          createdBy={createdBy}
          totalAmount={transaction.totalAmount}
          movementDate={transaction.movementDate}
          status={transaction.status}
        />
      ))}
    </Stack>
  );
};

export default TransactionCardList;
