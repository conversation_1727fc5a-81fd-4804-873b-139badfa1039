<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Epson Thermal Printer Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            background-color: #f8f9fa;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        .device-info {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖨️ Epson Thermal Printer Test</h1>
            <p>Test your Epson thermal printer compatibility with Web USB API</p>
        </div>

        <div class="test-section">
            <h3>Browser Compatibility</h3>
            <div id="browser-check"></div>
        </div>

        <div class="test-section">
            <h3>Quick Tests</h3>
            <button class="button" onclick="testWebUSB()">Test Web USB Support</button>
            <button class="button" onclick="detectDevices()">Detect Epson Devices</button>
            <button class="button" onclick="testConnection()">Test Connection</button>
            <button class="button" onclick="printTestReceipt()">Print Test Receipt</button>
        </div>

        <div class="test-section">
            <h3>Full Test Suite</h3>
            <button class="button" onclick="runFullTest()" id="fullTestBtn">Run Complete Test</button>
            <div id="progress-container" style="display: none;">
                <div class="progress">
                    <div class="progress-bar" id="progress-bar"></div>
                </div>
                <div id="current-test"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>Device Information</h3>
            <div id="device-info"></div>
        </div>

        <div class="test-section">
            <h3>Sample Receipt Preview</h3>
            <pre id="receipt-preview">
TIENDA DE PRUEBA
Calle Principal 123, Ciudad
+52 55 1234 5678

     RECIBO DE TRANSACCION
================================

Referencia:           WS-001-2024
Fecha:           01/12/24 - 14:30

--------------------------------
       DETALLE DE COMPRA
--------------------------------
Monto base:              $150.75
Intereses:                 $5.25
IVA:                      $24.12
--------------------------------
TOTAL:                   $180.12
================================

     INFORMACION DE PAGO
--------------------------------
Plazo:                   15 dias
Fecha limite:      16/12/24 - 14:30

    El cliente pagará después
  en el portal de Pagos de Propaga

================================
      Gracias por su compra
      Conserve este recibo
            </pre>
        </div>
    </div>

    <script>
        // Check browser compatibility on load
        window.onload = function() {
            checkBrowserCompatibility();
        };

        function checkBrowserCompatibility() {
            const browserCheck = document.getElementById('browser-check');
            
            if (!('usb' in navigator)) {
                browserCheck.innerHTML = `
                    <div class="error">❌ Web USB API not supported</div>
                    <div class="info">Please use Chrome, Edge, or Opera browser</div>
                `;
                return false;
            }

            browserCheck.innerHTML = `
                <div class="success">✅ Web USB API supported</div>
                <div class="info">Browser: ${navigator.userAgent.split(' ').pop()}</div>
            `;
            return true;
        }

        async function testWebUSB() {
            addResult('Testing Web USB API support...');
            
            if ('usb' in navigator) {
                addResult('✅ Web USB API is supported', 'success');
            } else {
                addResult('❌ Web USB API is not supported', 'error');
            }
        }

        async function detectDevices() {
            addResult('Detecting USB devices...');
            
            try {
                const devices = await navigator.usb.getDevices();
                const epsonDevices = devices.filter(device => device.vendorId === 0x04b8);
                
                if (epsonDevices.length > 0) {
                    addResult(`✅ Found ${epsonDevices.length} Epson device(s)`, 'success');
                    displayDeviceInfo(epsonDevices[0]);
                } else {
                    addResult('⚠️ No Epson devices found. Click to request device access.', 'info');
                    await requestDevice();
                }
            } catch (error) {
                addResult(`❌ Error detecting devices: ${error.message}`, 'error');
            }
        }

        async function requestDevice() {
            try {
                const device = await navigator.usb.requestDevice({
                    filters: [
                        { vendorId: 0x04b8 }, // Epson
                        { vendorId: 0x0519 }, // Star Micronics
                        { vendorId: 0x154f }, // Citizen
                    ]
                });
                
                addResult('✅ Device selected successfully', 'success');
                displayDeviceInfo(device);
                return device;
            } catch (error) {
                addResult(`❌ Device selection failed: ${error.message}`, 'error');
                return null;
            }
        }

        function displayDeviceInfo(device) {
            const deviceInfo = document.getElementById('device-info');
            deviceInfo.innerHTML = `
                <div class="device-info">
                    <h4>Connected Device</h4>
                    <p><strong>Vendor ID:</strong> 0x${device.vendorId.toString(16).padStart(4, '0')}</p>
                    <p><strong>Product ID:</strong> 0x${device.productId.toString(16).padStart(4, '0')}</p>
                    <p><strong>Product Name:</strong> ${device.productName || 'Unknown'}</p>
                    <p><strong>Manufacturer:</strong> ${device.manufacturerName || 'Unknown'}</p>
                    <p><strong>Serial Number:</strong> ${device.serialNumber || 'Unknown'}</p>
                </div>
            `;
        }

        async function testConnection() {
            addResult('Testing printer connection...');
            
            try {
                const device = await requestDevice();
                if (!device) return;

                await device.open();
                addResult('✅ Device opened successfully', 'success');

                if (device.configurations.length === 0) {
                    throw new Error('No configurations available');
                }

                await device.selectConfiguration(1);
                addResult('✅ Configuration selected', 'success');

                await device.claimInterface(0);
                addResult('✅ Interface claimed', 'success');

                await device.close();
                addResult('✅ Connection test completed successfully', 'success');
            } catch (error) {
                addResult(`❌ Connection test failed: ${error.message}`, 'error');
            }
        }

        async function printTestReceipt() {
            addResult('Printing test receipt...');
            
            try {
                const device = await requestDevice();
                if (!device) return;

                await device.open();
                await device.selectConfiguration(1);
                await device.claimInterface(0);

                // Get the interface and find the correct endpoint
                const config = device.configuration;
                const interface0 = config.interfaces[0];
                const alternate = interface0.alternates[0];
                const outEndpoint = alternate.endpoints.find(ep => ep.direction === 'out');

                if (!outEndpoint) {
                    throw new Error('No OUT endpoint found');
                }

                // Initialize printer
                const initCommands = new Uint8Array([
                    0x1b, 0x40, // ESC @ - Initialize
                    0x1b, 0x61, 0x00, // ESC a 0 - Left align
                ]);
                await device.transferOut(outEndpoint.endpointNumber, initCommands);

                // Send test content
                const testContent = document.getElementById('receipt-preview').textContent;
                const encoder = new TextEncoder();
                const data = encoder.encode(testContent);

                // Send in chunks
                const chunkSize = 64;
                for (let i = 0; i < data.length; i += chunkSize) {
                    const chunk = data.slice(i, i + chunkSize);
                    await device.transferOut(outEndpoint.endpointNumber, chunk);
                    await new Promise(resolve => setTimeout(resolve, 10));
                }

                // Cut paper
                const cutCommand = new Uint8Array([0x1d, 0x56, 0x00, 0x0a, 0x0a, 0x0a]);
                await device.transferOut(outEndpoint.endpointNumber, cutCommand);

                await device.close();
                addResult('✅ Test receipt printed successfully', 'success');
            } catch (error) {
                addResult(`❌ Print test failed: ${error.message}`, 'error');
            }
        }

        async function runFullTest() {
            const btn = document.getElementById('fullTestBtn');
            const progressContainer = document.getElementById('progress-container');
            const progressBar = document.getElementById('progress-bar');
            const currentTest = document.getElementById('current-test');
            
            btn.disabled = true;
            progressContainer.style.display = 'block';
            clearResults();

            const tests = [
                { name: 'Web USB Support', func: testWebUSB },
                { name: 'Device Detection', func: detectDevices },
                { name: 'Connection Test', func: testConnection },
                { name: 'Print Test', func: printTestReceipt },
            ];

            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                currentTest.textContent = `Running: ${test.name}...`;
                progressBar.style.width = `${((i + 1) / tests.length) * 100}%`;
                
                await test.func();
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            currentTest.textContent = 'All tests completed!';
            btn.disabled = false;
            progressContainer.style.display = 'none';
        }

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
    </script>
</body>
</html>
