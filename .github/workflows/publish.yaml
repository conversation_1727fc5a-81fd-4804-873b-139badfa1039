name: Deploy to ECR

on:
  push:
    branches: [main, develop, staging]

jobs:
  build:
    
    name: Build Image
    runs-on: ubuntu-latest

    steps:
    - name: Check out code
      uses: actions/checkout@v2
    
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-west-2
    - name: Get branch name
      id: refbranch
      shell: bash
      run: echo "::set-output name=branch::${GITHUB_REF#refs/heads/}"

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push image to Amazon ECR
      if: steps.refbranch.outputs.branch != 'main'
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: kraken
        IMAGE_TAG: ${{ steps.refbranch.outputs.branch }}
        APP_ENV: ${{ steps.refbranch.outputs.branch }}
      run: |
        docker build --build-arg APP_ENV=${{ steps.refbranch.outputs.branch }} -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

    - name: Build, tag, and push image to Amazon ECR production
      if: steps.refbranch.outputs.branch == 'main'
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: kraken
        IMAGE_TAG: production
        APP_ENV: production
      run: |
        docker build --build-arg APP_ENV=production -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

    - name: ECS refresh
      if: steps.refbranch.outputs.branch != 'main'
      uses: giboow/action-aws-cli@v1
      with:
        args: ecs update-service --cluster propaga-${{ steps.refbranch.outputs.branch }} --service kraken --force-new-deployment
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: "us-west-2"

    - name: ECS refresh production
      if: steps.refbranch.outputs.branch == 'main'
      uses: giboow/action-aws-cli@v1
      with:
        args: ecs update-service --cluster propaga-production --service kraken --force-new-deployment
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_DEFAULT_REGION: "us-west-2"
    