{"name": "kraken", "version": "0.1.0", "private": true, "scripts": {"copy-secret": "sh ./.secrets/copy.sh", "dev": "next dev -p 3005", "build": "npm run copy-secret && next build", "start": "npm run copy-secret && next start -p 3005", "lint": "eslint . --ext ts --ext tsx --ext js", "lint-fix": "eslint . --ext ts --ext tsx --ext js --fix", "prepare": "./node_modules/.bin/husky", "pre-commit": "lint-staged", "next-build": "next build"}, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/next-js": "^2.2.0", "@chakra-ui/react": "^2.8.2", "@clerk/localizations": "^2.6.1", "@clerk/nextjs": "^5.1.6", "@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@sentry/nextjs": "^8.26.0", "@tanstack/react-query": "^5.51.15", "@types/react-datepicker": "^7.0.0", "algoliasearch": "^5.20.2", "aws-sdk": "^2.1664.0", "axios": "^1.7.2", "escpos-buffer": "^2.0.2", "eslint-config-next": "^14.2.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "framer-motion": "^11.3.19", "js-cookie": "^3.0.5", "nanoid": "^5.0.7", "next": "^14.2.25", "react": "^18", "react-datepicker": "^8.0.0", "react-dom": "^18", "react-instantsearch": "^7.15.3", "react-number-format": "^5.4.0", "typescript": "^5"}, "devDependencies": {"@commitlint/config-conventional": "^19.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.8.0", "@typescript-eslint/parser": "^7.8.0", "commitlint": "^19.3.0", "eslint": "^8", "husky": "^9.1.4"}}